/// User model with role-based access control and preferences
/// Model người dùng với kiểm soát truy cập dựa trên vai trò và tùy chọn
import 'package:cloud_firestore/cloud_firestore.dart';

/// User role enumeration for access control
/// Enum vai trò người dùng để kiểm soát truy cập
enum UserRole { user, admin, developer }

extension UserRoleExtension on UserRole {
  String get name {
    switch (this) {
      case UserRole.user:
        return 'user';
      case UserRole.admin:
        return 'admin';
      case UserRole.developer:
        return 'developer';
    }
  }

  /// Convert string to UserRole enum
  /// Chuyển đổi chuỗi thành enum UserRole
  static UserRole fromString(String? value) {
    if (value == 'admin') return UserRole.admin;
    if (value == 'developer') return UserRole.developer;
    return UserRole.user; // Default role / Vai trò mặc định
  }
}

class UserAddress {
  final String? street;
  final String? city;
  final String? province;
  final String? zipCode;

  UserAddress({
    this.street,
    this.city,
    this.province,
    this.zipCode,
  });

  factory UserAddress.fromJson(Map<String, dynamic> json) {
    return UserAddress(
      street: json['street'],
      city: json['city'],
      province: json['province'],
      zipCode: json['zipCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'province': province,
      'zipCode': zipCode,
    };
  }

  UserAddress copyWith({
    String? street,
    String? city,
    String? province,
    String? zipCode,
  }) {
    return UserAddress(
      street: street ?? this.street,
      city: city ?? this.city,
      province: province ?? this.province,
      zipCode: zipCode ?? this.zipCode,
    );
  }
}

class UserNotificationPreferences {
  final bool email;
  final bool push;
  final bool sms;

  UserNotificationPreferences({
    this.email = true,
    this.push = true,
    this.sms = false,
  });

  factory UserNotificationPreferences.fromJson(Map<String, dynamic> json) {
    return UserNotificationPreferences(
      email: json['email'] ?? true,
      push: json['push'] ?? true,
      sms: json['sms'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'push': push,
      'sms': sms,
    };
  }

  UserNotificationPreferences copyWith({
    bool? email,
    bool? push,
    bool? sms,
  }) {
    return UserNotificationPreferences(
      email: email ?? this.email,
      push: push ?? this.push,
      sms: sms ?? this.sms,
    );
  }
}

class UserPreferences {
  final List<String> favoriteGenres;
  final String language;
  final UserNotificationPreferences notifications;

  UserPreferences({
    this.favoriteGenres = const [],
    this.language = 'vi',
    UserNotificationPreferences? notifications,
  }) : notifications = notifications ?? UserNotificationPreferences();

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      favoriteGenres: List<String>.from(json['favoriteGenres'] ?? []),
      language: json['language'] ?? 'vi',
      notifications: json['notifications'] != null
          ? UserNotificationPreferences.fromJson(json['notifications'])
          : UserNotificationPreferences(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'favoriteGenres': favoriteGenres,
      'language': language,
      'notifications': notifications.toJson(),
    };
  }

  UserPreferences copyWith({
    List<String>? favoriteGenres,
    String? language,
    UserNotificationPreferences? notifications,
  }) {
    return UserPreferences(
      favoriteGenres: favoriteGenres ?? this.favoriteGenres,
      language: language ?? this.language,
      notifications: notifications ?? this.notifications,
    );
  }
}

class UserModel {
  final String? id;
  final String? email;
  final String? name;
  final String? photoUrl;
  final String? phoneNumber;
  final DateTime? dateOfBirth;
  final String? gender; // "male", "female", "other"
  final UserAddress? address;
  final UserPreferences preferences;
  final int loyaltyPoints;
  final String membershipLevel; // "bronze", "silver", "gold", "platinum"
  final UserRole role;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLoginAt;
  final bool isActive;

  UserModel({
    this.id,
    this.email,
    this.name,
    this.photoUrl,
    this.phoneNumber,
    this.dateOfBirth,
    this.gender,
    this.address,
    UserPreferences? preferences,
    this.loyaltyPoints = 0,
    this.membershipLevel = 'bronze',
    this.role = UserRole.user,
    this.createdAt,
    this.updatedAt,
    this.lastLoginAt,
    this.isActive = true,
  }) : preferences = preferences ?? UserPreferences();

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      photoUrl: json['photoUrl'],
      phoneNumber: json['phoneNumber'],
      dateOfBirth: json['dateOfBirth'] != null
          ? (json['dateOfBirth'] as Timestamp).toDate()
          : null,
      gender: json['gender'],
      address: json['address'] != null
          ? UserAddress.fromJson(json['address'])
          : null,
      preferences: json['preferences'] != null
          ? UserPreferences.fromJson(json['preferences'])
          : UserPreferences(),
      loyaltyPoints: json['loyaltyPoints'] ?? 0,
      membershipLevel: json['membershipLevel'] ?? 'bronze',
      role: UserRoleExtension.fromString(json['role']),
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
      lastLoginAt: json['lastLoginAt'] != null
          ? (json['lastLoginAt'] as Timestamp).toDate()
          : null,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'photoUrl': photoUrl,
      'phoneNumber': phoneNumber,
      'dateOfBirth':
          dateOfBirth != null ? Timestamp.fromDate(dateOfBirth!) : null,
      'gender': gender,
      'address': address?.toJson(),
      'preferences': preferences.toJson(),
      'loyaltyPoints': loyaltyPoints,
      'membershipLevel': membershipLevel,
      'role': role.name,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'lastLoginAt':
          lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'isActive': isActive,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? photoUrl,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    UserAddress? address,
    UserPreferences? preferences,
    int? loyaltyPoints,
    String? membershipLevel,
    UserRole? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    bool? isActive,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      preferences: preferences ?? this.preferences,
      loyaltyPoints: loyaltyPoints ?? this.loyaltyPoints,
      membershipLevel: membershipLevel ?? this.membershipLevel,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
    );
  }

  bool get isAdmin => role == UserRole.admin || role == UserRole.developer;
  bool get isDeveloper => role == UserRole.developer;
}

// User Role Model for separate collection
class UserRoleModel {
  final String userId;
  final UserRole role;
  final String? assignedBy;
  final DateTime? assignedAt;
  final List<String> permissions;
  final bool isActive;

  UserRoleModel({
    required this.userId,
    required this.role,
    this.assignedBy,
    this.assignedAt,
    this.permissions = const [],
    this.isActive = true,
  });

  factory UserRoleModel.fromJson(Map<String, dynamic> json) {
    return UserRoleModel(
      userId: json['userId'],
      role: UserRoleExtension.fromString(json['role']),
      assignedBy: json['assignedBy'],
      assignedAt: json['assignedAt'] != null
          ? (json['assignedAt'] as Timestamp).toDate()
          : null,
      permissions: List<String>.from(json['permissions'] ?? []),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'role': role.name,
      'assignedBy': assignedBy,
      'assignedAt': assignedAt != null ? Timestamp.fromDate(assignedAt!) : null,
      'permissions': permissions,
      'isActive': isActive,
    };
  }

  UserRoleModel copyWith({
    String? userId,
    UserRole? role,
    String? assignedBy,
    DateTime? assignedAt,
    List<String>? permissions,
    bool? isActive,
  }) {
    return UserRoleModel(
      userId: userId ?? this.userId,
      role: role ?? this.role,
      assignedBy: assignedBy ?? this.assignedBy,
      assignedAt: assignedAt ?? this.assignedAt,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
    );
  }
}
