import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';

class FirebaseStorageService {
  static final FirebaseStorageService _instance =
      FirebaseStorageService._internal();
  factory FirebaseStorageService() => _instance;
  FirebaseStorageService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Upload user avatar image to Firebase Storage
  /// Returns the download URL of the uploaded image
  Future<String> uploadUserAvatar(String userId, File imageFile) async {
    try {
      // Create a reference to the location where we want to upload the file
      final String fileName =
          'avatar_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final Reference ref =
          _storage.ref().child('user_avatars').child(fileName);

      // Upload the file
      final UploadTask uploadTask = ref.putFile(
        imageFile,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'userId': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      // Wait for the upload to complete
      final TaskSnapshot snapshot = await uploadTask;

      // Get the download URL
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      if (kDebugMode) {
        print('Avatar uploaded successfully: $downloadUrl');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading avatar: $e');
      }
      throw Exception('Failed to upload avatar: $e');
    }
  }

  /// Upload user avatar from bytes (for web platform)
  Future<String> uploadUserAvatarFromBytes(
      String userId, Uint8List imageBytes, String fileName) async {
    try {
      // Create a reference to the location where we want to upload the file
      final String uploadFileName =
          'avatar_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final Reference ref =
          _storage.ref().child('user_avatars').child(uploadFileName);

      // Upload the file
      final UploadTask uploadTask = ref.putData(
        imageBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'userId': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
            'originalFileName': fileName,
          },
        ),
      );

      // Wait for the upload to complete
      final TaskSnapshot snapshot = await uploadTask;

      // Get the download URL
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      if (kDebugMode) {
        print('Avatar uploaded successfully from bytes: $downloadUrl');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading avatar from bytes: $e');
      }
      throw Exception('Failed to upload avatar: $e');
    }
  }

  /// Delete user avatar from Firebase Storage
  Future<bool> deleteUserAvatar(String downloadUrl) async {
    try {
      // Extract the file path from the download URL
      final Reference ref = _storage.refFromURL(downloadUrl);

      // Delete the file
      await ref.delete();

      if (kDebugMode) {
        print('Avatar deleted successfully: $downloadUrl');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting avatar: $e');
      }
      return false;
    }
  }

  /// Get all user avatars (for admin purposes)
  Future<List<String>> getUserAvatars(String userId) async {
    try {
      final ListResult result =
          await _storage.ref().child('user_avatars').listAll();

      final List<String> avatarUrls = [];

      for (final Reference ref in result.items) {
        // Check if this avatar belongs to the user
        final FullMetadata metadata = await ref.getMetadata();
        if (metadata.customMetadata?['userId'] == userId) {
          final String downloadUrl = await ref.getDownloadURL();
          avatarUrls.add(downloadUrl);
        }
      }

      return avatarUrls;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user avatars: $e');
      }
      return [];
    }
  }

  /// Clean up old avatars for a user (keep only the latest 3)
  Future<void> cleanupOldAvatars(String userId) async {
    try {
      final List<String> avatarUrls = await getUserAvatars(userId);

      if (avatarUrls.length > 3) {
        // Sort by upload time and delete the oldest ones
        final List<Reference> refs = [];
        for (final String url in avatarUrls) {
          refs.add(_storage.refFromURL(url));
        }

        // Sort by creation time
        refs.sort((a, b) {
          // This is a simplified sort - in a real app you might want to use metadata
          return a.name.compareTo(b.name);
        });

        // Delete the oldest avatars (keep only the latest 3)
        for (int i = 0; i < refs.length - 3; i++) {
          await refs[i].delete();
          if (kDebugMode) {
            print('Deleted old avatar: ${refs[i].name}');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up old avatars: $e');
      }
    }
  }
}
