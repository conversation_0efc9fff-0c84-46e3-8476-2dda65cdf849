import 'package:shared_preferences/shared_preferences.dart';

/// Lớp tiện ích để hỗ trợ dọn dẹp storage
class StorageCleanup {
  /// <PERSON><PERSON>a bắt buộc tất cả dữ liệu SharedPreferences
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      // Bỏ qua lỗi trong quá trình dọn dẹp
    }
  }

  /// Chỉ xóa dữ liệu liên quan đến user
  static Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Xóa các key liên quan đến user
      if (prefs.containsKey('user')) {
        await prefs.remove('user');
      }
      if (prefs.containsKey('isLoggedIn')) {
        await prefs.remove('isLoggedIn');
      }
    } catch (e) {
      // Bỏ qua lỗi trong quá trình dọn dẹp
    }
  }

  /// Kiểm tra và xóa dữ liệu có vấn đề
  static Future<void> checkAndFixProblematicData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Kiểm tra các key có thể gây vấn đề
      final allKeys = prefs.getKeys();
      for (final key in allKeys) {
        try {
          // Thử truy cập value - nếu gây lỗi thì xóa key
          prefs.get(key);
        } catch (e) {
          // Nếu truy cập value gây lỗi, xóa nó
          await prefs.remove(key);
        }
      }
    } catch (e) {
      // Nếu có lỗi trong quá trình kiểm tra, xóa tất cả dữ liệu
      await clearAllData();
    }
  }
}
