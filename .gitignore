# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Firebase and sensitive configuration files
firebase_options.dart
google-services.json
GoogleService-Info.plist
.env
.env.local
.env.production
.env.staging

# API keys and secrets
android/key.properties
android/local.properties
ios/Runner/GoogleService-Info.plist
ios/firebase_app_id_file.json

# Firebase emulator data
.firebase/
firebase-debug.log
firestore-debug.log
ui-debug.log

# Additional Flutter/Dart files
*.g.dart
*.freezed.dart
*.mocks.dart
coverage/
test/coverage/

# Platform specific
.vscode/
*.pid

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Node.js (for Firebase functions)
functions/node_modules/
functions/.env
my_codebase/node_modules/
my_codebase/.env

# Documentation build files
docs/_build/
