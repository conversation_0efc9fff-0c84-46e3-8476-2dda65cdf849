import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:dop_phim/controllers/schedule_controller.dart';
import 'package:dop_phim/models/movie_model.dart';
import 'package:dop_phim/models/theater_model.dart';
import 'package:dop_phim/models/screen_model.dart' as screen;
import 'package:dop_phim/models/showtime_model.dart';
import 'package:dop_phim/services/screen_service.dart';

class ScheduleFormPage extends StatefulWidget {
  final ShowtimeModel? showtime; // null for create, non-null for edit

  const ScheduleFormPage({Key? key, this.showtime}) : super(key: key);

  @override
  State<ScheduleFormPage> createState() => _ScheduleFormPageState();
}

class _ScheduleFormPageState extends State<ScheduleFormPage> {
  final ScheduleController _scheduleController = Get.find<ScheduleController>();
  final PageController _pageController = PageController();
  final RxInt _currentStep = 0.obs;

  // Search controllers
  final TextEditingController _movieSearchController = TextEditingController();
  final TextEditingController _theaterSearchController =
      TextEditingController();
  String _movieSearchQuery = '';
  String _theaterSearchQuery = '';
  MovieStatus? _selectedMovieStatus;

  bool get isEditMode => widget.showtime != null;

  @override
  void initState() {
    super.initState();

    // Delay initialization to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    try {
      // Load data without triggering UI updates during build
      await Future.wait([
        _scheduleController.loadMovies(),
        _scheduleController.loadTheaters(),
      ]);

      if (isEditMode) {
        _initializeEditMode();
      }
    } catch (e) {
      print('Error loading initial data: $e');
    }
  }

  void _initializeEditMode() {
    final showtime = widget.showtime!;

    // Use Future.delayed to avoid setState during build
    Future.delayed(Duration.zero, () async {
      // Set selected movie
      final movie = _scheduleController.getMovieById(showtime.movieId);
      if (movie != null) {
        _scheduleController.setSelectedMovie(movie);
      }

      // Set selected theater
      final theater = _scheduleController.getTheaterById(showtime.theaterId);
      if (theater != null) {
        _scheduleController.selectedTheater.value = theater;
        _scheduleController.selectedScreenIds.clear();
        _scheduleController.conflicts.clear();
        _scheduleController.suggestedTimes.clear();

        // Load screens and set selected screen
        await _scheduleController.loadScreensForTheater(theater.id);
        _scheduleController.selectedScreenIds.add(showtime.screenId);
      }

      // Set selected date and time
      _scheduleController.selectedDates.clear();
      _scheduleController.selectedDates.add(showtime.date);
      _scheduleController.selectedTimes.clear();
      _scheduleController.selectedTimes.add(showtime.time);
    });
  }

  @override
  void dispose() {
    _movieSearchController.dispose();
    _theaterSearchController.dispose();
    _scheduleController.resetFormData();
    super.dispose();
  }

  List<Movie> _getFilteredMovies() {
    var movies = _scheduleController.movies.toList();

    // Filter by status if selected
    if (_selectedMovieStatus != null) {
      movies = movies
          .where((movie) => movie.status == _selectedMovieStatus)
          .toList();
    }

    // Filter by search query
    if (_movieSearchQuery.isNotEmpty) {
      movies = movies.where((movie) {
        return movie.title
                .toLowerCase()
                .contains(_movieSearchQuery.toLowerCase()) ||
            movie.genres.any((genre) =>
                genre.toLowerCase().contains(_movieSearchQuery.toLowerCase()));
      }).toList();
    }

    return movies;
  }

  List<TheaterModel> _getFilteredTheaters() {
    if (_theaterSearchQuery.isEmpty) {
      return _scheduleController.theaters;
    }

    return _scheduleController.theaters.where((theater) {
      return theater.name
              .toLowerCase()
              .contains(_theaterSearchQuery.toLowerCase()) ||
          theater.address.fullAddress
              .toLowerCase()
              .contains(_theaterSearchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff1a1a2e),
      appBar: AppBar(
        title: Text(
          isEditMode ? 'Chỉnh sửa lịch chiếu' : 'Tạo lịch chiếu mới',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xff16213e),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Step indicators
          Container(
            padding: const EdgeInsets.all(16),
            child: Obx(() => Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildStepIndicator(0, 'Phim', _currentStep.value >= 0),
                    _buildStepConnector(_currentStep.value >= 1),
                    _buildStepIndicator(1, 'Rạp', _currentStep.value >= 1),
                    _buildStepConnector(_currentStep.value >= 2),
                    _buildStepIndicator(2, 'Phòng', _currentStep.value >= 2),
                    _buildStepConnector(_currentStep.value >= 3),
                    _buildStepIndicator(3, 'Lịch', _currentStep.value >= 3),
                    _buildStepConnector(_currentStep.value >= 4),
                    _buildStepIndicator(4, 'Xác nhận', _currentStep.value >= 4),
                  ],
                )),
          ),

          // Page content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildMovieSelectionStep(),
                _buildTheaterSelectionStep(),
                _buildScreenSelectionStep(),
                _buildScheduleSelectionStep(),
                _buildConfirmationStep(),
              ],
            ),
          ),

          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Obx(() => Row(
                  children: [
                    if (_currentStep.value > 0)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _previousStep,
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Colors.white),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: Text(
                            'Quay lại',
                            style:
                                GoogleFonts.mulish(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    if (_currentStep.value > 0) const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _canProceed() ? _nextStep : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          _getNextButtonText(),
                          style:
                              GoogleFonts.mulish(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, bool isActive) {
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: isActive ? Colors.blue : Colors.grey.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: GoogleFonts.mulish(
            color: isActive ? Colors.white : Colors.grey,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStepConnector(bool isActive) {
    return Container(
      width: 24,
      height: 2,
      margin: const EdgeInsets.only(bottom: 20),
      color: isActive ? Colors.blue : Colors.grey.withOpacity(0.3),
    );
  }

  // Movie selection step
  Widget _buildMovieSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn phim',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          // Movie search bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: TextField(
              controller: _movieSearchController,
              autofocus: false, // Explicitly disable autofocus
              style: GoogleFonts.mulish(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Tìm kiếm phim theo tên hoặc thể loại...',
                hintStyle: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.6),
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.white.withOpacity(0.7),
                ),
                suffixIcon: _movieSearchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _movieSearchController.clear();
                          setState(() {
                            _movieSearchQuery = '';
                          });
                        },
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _movieSearchQuery = value;
                });
              },
            ),
          ),
          const SizedBox(height: 16),

          // Status filter chips
          if (_movieSearchQuery.isEmpty)
            Container(
              height: 40,
              margin: const EdgeInsets.only(bottom: 16),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildStatusFilterChip('Tất cả', null),
                  const SizedBox(width: 8),
                  _buildStatusFilterChip('Đang chiếu', MovieStatus.nowPlaying),
                  const SizedBox(width: 8),
                  _buildStatusFilterChip('Sắp chiếu', MovieStatus.upcoming),
                ],
              ),
            ),

          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              final filteredMovies = _getFilteredMovies();

              if (filteredMovies.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _movieSearchQuery.isNotEmpty
                            ? Icons.search_off
                            : Icons.movie,
                        size: 64,
                        color: Colors.white.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _movieSearchQuery.isNotEmpty
                            ? 'Không tìm thấy phim nào với từ khóa "$_movieSearchQuery"'
                            : 'Không có phim nào',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (_movieSearchQuery.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () {
                            _movieSearchController.clear();
                            setState(() {
                              _movieSearchQuery = '';
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white.withOpacity(0.1),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            'Xóa tìm kiếm',
                            style: GoogleFonts.mulish(),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  if (_movieSearchQuery.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        'Tìm thấy ${filteredMovies.length} phim',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: filteredMovies.length,
                      itemBuilder: (context, index) {
                        final movie = filteredMovies[index];
                        return _buildMovieCard(movie);
                      },
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildMovieCard(Movie movie) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedMovie.value?.id == movie.id;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              movie.fullPosterPath,
              width: 50,
              height: 70,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 50,
                height: 70,
                color: Colors.grey,
                child: const Icon(Icons.movie, color: Colors.white),
              ),
            ),
          ),
          title: Text(
            movie.title,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${movie.runtime ?? 0} phút • ${movie.genres.join(', ')}',
                style: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: movie.status == MovieStatus.nowPlaying
                      ? Colors.green.withOpacity(0.2)
                      : Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: movie.status == MovieStatus.nowPlaying
                        ? Colors.green.withOpacity(0.5)
                        : Colors.orange.withOpacity(0.5),
                  ),
                ),
                child: Text(
                  movie.status.displayName,
                  style: GoogleFonts.mulish(
                    fontSize: 10,
                    color: movie.status == MovieStatus.nowPlaying
                        ? Colors.green
                        : Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.blue)
              : null,
          onTap: () => _scheduleController.setSelectedMovie(movie),
        ),
      );
    });
  }

  // Theater selection step
  Widget _buildTheaterSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Chọn rạp chiếu',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              GestureDetector(
                onTap: () => _scheduleController.loadTheaters(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                  ),
                  child: const Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Theater search bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: TextField(
              controller: _theaterSearchController,
              autofocus: false, // Explicitly disable autofocus
              style: GoogleFonts.mulish(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Tìm kiếm rạp theo tên hoặc địa chỉ...',
                hintStyle: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.6),
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.white.withOpacity(0.7),
                ),
                suffixIcon: _theaterSearchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _theaterSearchController.clear();
                          setState(() {
                            _theaterSearchQuery = '';
                          });
                        },
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _theaterSearchQuery = value;
                });
              },
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              final filteredTheaters = _getFilteredTheaters();

              if (filteredTheaters.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _theaterSearchQuery.isNotEmpty
                            ? Icons.search_off
                            : Icons.local_movies,
                        size: 64,
                        color: Colors.white.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _theaterSearchQuery.isNotEmpty
                            ? 'Không tìm thấy rạp nào với từ khóa "$_theaterSearchQuery"'
                            : 'Không có rạp nào',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (_theaterSearchQuery.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () {
                            _theaterSearchController.clear();
                            setState(() {
                              _theaterSearchQuery = '';
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white.withOpacity(0.1),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            'Xóa tìm kiếm',
                            style: GoogleFonts.mulish(),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  if (_theaterSearchQuery.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        'Tìm thấy ${filteredTheaters.length} rạp',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: filteredTheaters.length,
                      itemBuilder: (context, index) {
                        final theater = filteredTheaters[index];
                        return _buildTheaterCard(theater);
                      },
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTheaterCard(TheaterModel theater) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedTheater.value?.id == theater.id;
      final isActive = theater.isActive;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.3)
              : isActive
                  ? Colors.white.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.blue
                : isActive
                    ? Colors.white.withOpacity(0.2)
                    : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ListTile(
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isActive
                  ? Colors.blue.withOpacity(0.2)
                  : Colors.grey.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.local_movies,
              color: isActive ? Colors.blue : Colors.grey,
            ),
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  theater.name,
                  style: GoogleFonts.mulish(
                    color: isActive ? Colors.white : Colors.grey,
                    fontWeight: FontWeight.bold,
                    decoration: isActive ? null : TextDecoration.lineThrough,
                  ),
                ),
              ),
              if (!isActive) ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.5)),
                  ),
                  child: Text(
                    'Tạm dừng',
                    style: GoogleFonts.mulish(
                      fontSize: 10,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          subtitle: Text(
            theater.address.fullAddress,
            style: GoogleFonts.mulish(
              color: isActive
                  ? Colors.white.withOpacity(0.7)
                  : Colors.grey.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.blue)
              : Icon(
                  Icons.arrow_forward_ios,
                  color: isActive ? Colors.white : Colors.grey,
                  size: 16,
                ),
          onTap: isActive
              ? () {
                  _scheduleController.setSelectedTheater(theater);
                }
              : null,
        ),
      );
    });
  }

  // Screen selection step
  Widget _buildScreenSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn phòng chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              if (_scheduleController.selectedTheater.value == null) {
                return Center(
                  child: Text(
                    'Vui lòng chọn rạp trước',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              if (_scheduleController.screens.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.movie_outlined,
                        size: 64,
                        color: Colors.white.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Rạp này chưa có phòng chiếu nào',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                );
              }

              final activeScreens =
                  _scheduleController.screens.where((s) => s.isActive).toList();
              final inactiveScreens = _scheduleController.screens
                  .where((s) => !s.isActive)
                  .toList();

              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (activeScreens.isNotEmpty) ...[
                      Text(
                        'Phòng chiếu hoạt động:',
                        style: GoogleFonts.mulish(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: activeScreens
                            .map((screen) => _buildScreenChip(screen))
                            .toList(),
                      ),
                    ],
                    if (inactiveScreens.isNotEmpty) ...[
                      if (activeScreens.isNotEmpty) const SizedBox(height: 16),
                      Text(
                        'Phòng chiếu tạm dừng:',
                        style: GoogleFonts.mulish(
                          color: Colors.grey,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: inactiveScreens
                            .map((screen) => _buildScreenChip(screen))
                            .toList(),
                      ),
                    ],
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildScreenChip(screen.ScreenModel screenModel) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedScreenIds.contains(screenModel.id);
      final isActive = screenModel.isActive;

      return GestureDetector(
        onTap: isActive
            ? () {
                if (isEditMode) {
                  // In edit mode, only allow single selection
                  _scheduleController.selectedScreenIds.clear();
                  _scheduleController.selectedScreenIds.add(screenModel.id);
                } else {
                  _scheduleController.toggleScreenSelection(screenModel.id);
                }
              }
            : null,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? Colors.blue
                : isActive
                    ? Colors.white.withOpacity(0.1)
                    : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? Colors.blue
                  : isActive
                      ? Colors.white.withOpacity(0.3)
                      : Colors.grey.withOpacity(0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                screenModel.type.icon,
                size: 14,
                color: isSelected
                    ? Colors.white
                    : isActive
                        ? screenModel.type.color
                        : Colors.grey,
              ),
              const SizedBox(width: 6),
              Text(
                screenModel.name,
                style: GoogleFonts.mulish(
                  color: isSelected
                      ? Colors.white
                      : isActive
                          ? Colors.white.withOpacity(0.8)
                          : Colors.grey,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  decoration: isActive ? null : TextDecoration.lineThrough,
                ),
              ),
              if (!isActive) ...[
                const SizedBox(width: 4),
                const Icon(
                  Icons.block,
                  size: 12,
                  color: Colors.grey,
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  // Schedule selection step
  Widget _buildScheduleSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn lịch chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDateSelection(),
                  const SizedBox(height: 24),
                  _buildTimeSelection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn ngày chiếu:',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount:
                isEditMode ? 1 : 30, // In edit mode, only show current date
            itemBuilder: (context, index) {
              final date = isEditMode
                  ? DateTime.parse(widget.showtime!.date)
                  : DateTime.now().add(Duration(days: index));
              final dateString = _formatDate(date);

              return Obx(() {
                final isSelected =
                    _scheduleController.selectedDates.contains(dateString);

                return GestureDetector(
                  onTap: () {
                    if (isEditMode) {
                      // In edit mode, date is fixed
                      return;
                    }
                    if (isSelected) {
                      _scheduleController.removeSelectedDate(dateString);
                    } else {
                      _scheduleController.addSelectedDate(date);
                    }
                  },
                  child: Container(
                    width: 70,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.blue
                          : Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue
                            : Colors.white.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _getDayName(date),
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${date.day}',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _getMonthName(date),
                          style: GoogleFonts.mulish(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn giờ chiếu:',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        if (isEditMode) ...[
          // In edit mode, show current time as selected
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue),
            ),
            child: Text(
              widget.showtime!.time,
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ] else ...[
          FutureBuilder<List<String>>(
            future: _scheduleController.getPopularTimeSlots(),
            builder: (context, snapshot) {
              if (!snapshot.hasData) {
                return const CircularProgressIndicator(color: Colors.white);
              }

              return Obx(() => Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: snapshot.data!.map((time) {
                      final isSelected =
                          _scheduleController.selectedTimes.contains(time);

                      return GestureDetector(
                        onTap: () {
                          if (isSelected) {
                            _scheduleController.removeSelectedTime(time);
                          } else {
                            _scheduleController.addSelectedTime(time);
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.blue
                                : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isSelected
                                  ? Colors.blue
                                  : Colors.white.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            time,
                            style: GoogleFonts.mulish(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ));
            },
          ),
        ],
      ],
    );
  }

  // Confirmation step
  Widget _buildConfirmationStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isEditMode ? 'Xác nhận chỉnh sửa' : 'Xác nhận lịch chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildSummaryCard(),
                  const SizedBox(height: 16),
                  if (!isEditMode) _buildConflictCheck(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Obx(() {
      final movie = _scheduleController.selectedMovie.value;
      final theater = _scheduleController.selectedTheater.value;
      final selectedScreens = _scheduleController.screens
          .where((s) => _scheduleController.selectedScreenIds.contains(s.id))
          .toList();

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tóm tắt lịch chiếu',
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 12),
            if (movie != null) ...[
              _buildSummaryRow('Phim:', movie.title),
              const SizedBox(height: 8),
            ],
            if (theater != null) ...[
              _buildSummaryRow('Rạp:', theater.name),
              const SizedBox(height: 8),
            ],
            if (selectedScreens.isNotEmpty) ...[
              _buildSummaryRow('Phòng chiếu:',
                  selectedScreens.map((s) => s.name).join(', ')),
              const SizedBox(height: 8),
            ],
            if (_scheduleController.selectedDates.isNotEmpty) ...[
              _buildSummaryRow('Ngày chiếu:',
                  '${_scheduleController.selectedDates.length} ngày'),
              const SizedBox(height: 8),
            ],
            if (_scheduleController.selectedTimes.isNotEmpty) ...[
              _buildSummaryRow(
                  'Giờ chiếu:', _scheduleController.selectedTimes.join(', ')),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildSummaryRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: GoogleFonts.mulish(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConflictCheck() {
    return Obx(() {
      if (_scheduleController.conflicts.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withOpacity(0.5)),
          ),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Không có xung đột lịch chiếu',
                  style: GoogleFonts.mulish(
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withOpacity(0.5)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 12),
                Text(
                  'Phát hiện xung đột lịch chiếu',
                  style: GoogleFonts.mulish(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ..._scheduleController.conflicts.map((conflict) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• $conflict',
                    style: GoogleFonts.mulish(
                      color: Colors.red.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                )),
          ],
        ),
      );
    });
  }

  // Navigation methods
  void _nextStep() {
    if (_currentStep.value < 4) {
      if (_currentStep.value == 3 && !isEditMode) {
        // Check conflicts before going to confirmation
        _scheduleController.checkScheduleConflicts();
      }
      _currentStep.value++;
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Create or update schedule
      if (isEditMode) {
        _updateSchedule();
      } else {
        _createSchedule();
      }
    }
  }

  void _previousStep() {
    if (_currentStep.value > 0) {
      _currentStep.value--;
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceed() {
    final step = _currentStep.value;
    switch (step) {
      case 0:
        return _scheduleController.selectedMovie.value != null;
      case 1:
        return _scheduleController.selectedTheater.value != null &&
            _scheduleController.selectedTheater.value!.isActive;
      case 2:
        return _scheduleController.selectedScreenIds.isNotEmpty;
      case 3:
        return _scheduleController.selectedDates.isNotEmpty &&
            _scheduleController.selectedTimes.isNotEmpty;
      case 4:
        return isEditMode || _scheduleController.canCreateSchedule;
      default:
        return false;
    }
  }

  String _getNextButtonText() {
    switch (_currentStep.value) {
      case 0:
        return 'Chọn rạp';
      case 1:
        return 'Chọn phòng';
      case 2:
        return 'Chọn lịch';
      case 3:
        return 'Kiểm tra';
      case 4:
        return isEditMode ? 'Cập nhật lịch chiếu' : 'Tạo lịch chiếu';
      default:
        return 'Tiếp tục';
    }
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _getDayName(DateTime date) {
    const days = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    return days[date.weekday % 7];
  }

  String _getMonthName(DateTime date) {
    const months = [
      'T1',
      'T2',
      'T3',
      'T4',
      'T5',
      'T6',
      'T7',
      'T8',
      'T9',
      'T10',
      'T11',
      'T12'
    ];
    return months[date.month - 1];
  }

  Widget _buildStatusFilterChip(String label, MovieStatus? status) {
    final isSelected = _selectedMovieStatus == status;
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.mulish(
          color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          fontSize: 12,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedMovieStatus = selected ? status : null;
        });
      },
      backgroundColor: Colors.white.withOpacity(0.1),
      selectedColor: Colors.blue.withOpacity(0.3),
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected ? Colors.blue : Colors.white.withOpacity(0.3),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }

  // Create new schedule
  void _createSchedule() async {
    final success = await _scheduleController.createSchedule();
    if (success) {
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã tạo lịch chiếu mới',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  // Update existing schedule
  void _updateSchedule() async {
    if (widget.showtime == null) return;

    final updatedShowtime = widget.showtime!.copyWith(
      // Note: In edit mode, we typically only allow changing pricing or status
      // For this implementation, we'll keep it simple and just update the showtime
      updatedAt: DateTime.now(),
    );

    final success = await _scheduleController.updateShowtime(updatedShowtime);
    if (success) {
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã cập nhật lịch chiếu',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }
}
