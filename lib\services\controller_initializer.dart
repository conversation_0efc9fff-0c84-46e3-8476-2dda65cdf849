import 'package:get/get.dart';
import '../controllers/movie_controller.dart';
import '../controllers/favorite_controller.dart';
import '../controllers/ticket_controller.dart';
import '../controllers/booking_controller.dart';
import '../controllers/schedule_controller.dart';
import '../controllers/banner_controller.dart';

import '../controllers/realtime_notification_controller.dart';
import '../controllers/realtime_bug_report_controller.dart';
import '../bindings/realtime_database_binding.dart';
import '../utils/developer_mode.dart';

class ControllerInitializer {
  static bool _isInitialized = false;

  /// Khởi tạo tất cả controllers sau khi xác thực thành công
  static Future<void> initializeControllers() async {
    if (_isInitialized) return;

    try {
      // Khởi tạo core controllers trước
      Get.put(MovieController());
      Get.put(FavoriteController());
      Get.put(TicketController());

      // Khởi tạo controllers liên quan đến booking
      Get.put(BookingController());
      Get.put(ScheduleController());

      // Khởi tạo UI controllers
      Get.put(BannerController());

      // Khởi tạo Realtime Database controllers
      Get.put(RealtimeNotificationController());
      Get.put(RealtimeBugReportController());

      // Khởi tạo developer mode
      Get.put(DeveloperMode());

      // Khởi tạo bindings
      RealtimeDatabaseBinding().dependencies();

      _isInitialized = true;
    } catch (e) {
      // Ghi log lỗi nhưng không throw để tránh crash app
      print('Error initializing controllers: $e');
    }
  }

  /// Reset initialization state (useful for logout)
  static void reset() {
    _isInitialized = false;
  }

  /// Check if controllers are initialized
  static bool get isInitialized => _isInitialized;

  /// Safe get controller - initialize if not exists
  static T safeGet<T>() {
    try {
      return Get.find<T>();
    } catch (e) {
      // If controller doesn't exist, try to initialize all controllers first
      if (!_isInitialized) {
        initializeControllers();
      }

      // Try to get the specific controller
      try {
        return Get.find<T>();
      } catch (e2) {
        // If still not found, create the specific controller
        if (T == MovieController) {
          return Get.put(MovieController()) as T;
        } else if (T == FavoriteController) {
          return Get.put(FavoriteController()) as T;
        } else if (T == TicketController) {
          return Get.put(TicketController()) as T;
        } else if (T == BookingController) {
          return Get.put(BookingController()) as T;
        } else if (T == ScheduleController) {
          return Get.put(ScheduleController()) as T;
        } else if (T == BannerController) {
          return Get.put(BannerController()) as T;
        } else if (T == RealtimeNotificationController) {
          return Get.put(RealtimeNotificationController()) as T;
        } else if (T == RealtimeBugReportController) {
          return Get.put(RealtimeBugReportController()) as T;
        } else if (T == DeveloperMode) {
          return Get.put(DeveloperMode()) as T;
        } else {
          rethrow;
        }
      }
    }
  }
}
