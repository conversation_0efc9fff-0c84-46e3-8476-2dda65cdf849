name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test Flutter App
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
      
    - name: Analyze project source
      run: flutter analyze
      
    - name: Run tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        
  build-android:
    name: Build Android APK
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build APK
      run: flutter build apk --release
      
    - name: Upload APK artifact
      uses: actions/upload-artifact@v3
      with:
        name: release-apk
        path: build/app/outputs/flutter-apk/app-release.apk
        
  build-ios:
    name: Build iOS App
    runs-on: macos-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build iOS (no codesign)
      run: flutter build ios --release --no-codesign
      
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
        
  firebase-deploy:
    name: Deploy Firebase Functions
    runs-on: ubuntu-latest
    needs: [test, build-android]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install Firebase CLI
      run: npm install -g firebase-tools
      
    - name: Install Functions dependencies
      run: |
        cd functions
        npm ci
        
    - name: Deploy Firebase Functions
      run: firebase deploy --only functions
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        
  deploy-rules:
    name: Deploy Firebase Rules
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install Firebase CLI
      run: npm install -g firebase-tools
      
    - name: Deploy Firestore Rules
      run: firebase deploy --only firestore:rules
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        
    - name: Deploy Storage Rules
      run: firebase deploy --only storage
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        
    - name: Deploy Database Rules
      run: firebase deploy --only database
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [firebase-deploy, deploy-rules]
    if: always() && github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Notify success
      if: needs.firebase-deploy.result == 'success' && needs.deploy-rules.result == 'success'
      run: echo "✅ Deployment successful!"
      
    - name: Notify failure
      if: needs.firebase-deploy.result == 'failure' || needs.deploy-rules.result == 'failure'
      run: echo "❌ Deployment failed!"
