import 'dart:async';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/movie_controller.dart';
import '../../models/movie_model.dart';
import '../../utils/app_colors.dart';

class SplashScreen extends StatefulWidget {
  final Widget nextScreen;

  const SplashScreen({
    Key? key,
    required this.nextScreen,
  }) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // Movie controller - initialize if not exists
  late final MovieController _movieController;

  // Animation controller for the app title
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Current banner index for showing the corresponding movie title
  final RxInt _currentBannerIndex = 0.obs;

  @override
  void initState() {
    super.initState();

    // Initialize MovieController if not exists
    try {
      _movieController = Get.find<MovieController>();
    } catch (e) {
      // If MovieController doesn't exist, create it
      _movieController = Get.put(MovieController());
    }

    // Fetch splash movie banners
    print('SplashScreen: Initializing and fetching splash movie banners');
    _movieController.fetchSplashBannerMovies();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // Create fade-in animation
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    // Start animation
    _animationController.forward();

    // Navigate to the next screen after 8 seconds to give users time to see the banners
    Timer(const Duration(seconds: 8), () {
      Get.off(() => widget.nextScreen, transition: Transition.fadeIn);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          height: MediaQuery.of(context).size.height,
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradientVertical,
          ),
          child: Stack(
            children: [
              // Movie banners carousel
              Positioned.fill(
                child: Obx(() {
                  final movieBanners = _movieController.splashBannerMovies;

                  if (_movieController.isLoadingFirebaseMovies.value) {
                    return const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    );
                  }

                  if (movieBanners.isEmpty) {
                    return Center(
                      child: Text(
                        'Không có banner phim nào',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          color: Colors.white,
                        ),
                      ),
                    );
                  }

                  // Display movie banners
                  return CarouselSlider.builder(
                    options: CarouselOptions(
                      height: MediaQuery.of(context).size.height,
                      viewportFraction: 1.0,
                      autoPlay: true,
                      enlargeCenterPage: false,
                      autoPlayInterval: const Duration(seconds: 2),
                      autoPlayAnimationDuration:
                          const Duration(milliseconds: 800),
                      autoPlayCurve: Curves.fastOutSlowIn,
                      onPageChanged: (index, reason) {
                        // Update the current banner index
                        _currentBannerIndex.value = index;
                      },
                    ),
                    itemCount: movieBanners.length,
                    itemBuilder: (context, index, realIndex) {
                      final movie = movieBanners[index];
                      return Container(
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: NetworkImage(movie.fullBackdropPath),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(
                              Colors.black.withOpacity(0.6),
                              BlendMode.darken,
                            ),
                          ),
                        ),
                      );
                    },
                  );
                }),
              ),

              // Overlay gradient for better text visibility
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        const Color(0xff2B5876).withOpacity(0.3),
                        const Color(0xff4E4376).withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
              ),

              // App title and tagline with animation
              Center(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Đớp Phim',
                        style: GoogleFonts.mulish(
                          fontSize: 48,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              blurRadius: 10.0,
                              color: Colors.black.withOpacity(0.5),
                              offset: const Offset(2.0, 2.0),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Khám phá bộ phim yêu thích tiếp theo của bạn',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Colors.white.withOpacity(0.9),
                          shadows: [
                            Shadow(
                              blurRadius: 8.0,
                              color: Colors.black.withOpacity(0.5),
                              offset: const Offset(1.0, 1.0),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),
                      // Show current movie title
                      Obx(() {
                        final movieBanners =
                            _movieController.splashBannerMovies;

                        if (movieBanners.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        final index =
                            _currentBannerIndex.value % movieBanners.length;
                        final title = movieBanners[index].title;

                        return AnimatedSwitcher(
                          duration: const Duration(milliseconds: 500),
                          child: Text(
                            title,
                            key: ValueKey<int>(_currentBannerIndex.value),
                            style: GoogleFonts.mulish(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.amber,
                              shadows: [
                                Shadow(
                                  blurRadius: 8.0,
                                  color: Colors.black.withOpacity(0.7),
                                  offset: const Offset(1.0, 1.0),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              // Loading indicator at the bottom
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      const SizedBox(height: 20),
                      Obx(() {
                        final movieBanners =
                            _movieController.splashBannerMovies;

                        if (movieBanners.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        final index =
                            _currentBannerIndex.value % movieBanners.length;
                        final title = movieBanners[index].title;

                        return Text(
                          'Đang chiếu: $title',
                          style: GoogleFonts.mulish(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        );
                      }),
                      const SizedBox(height: 8),
                      Text(
                        'Đang tải những bộ phim tuyệt vời cho bạn...',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Skip button
              Positioned(
                top: 50,
                right: 20,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: TextButton(
                    onPressed: () {
                      // Skip the splash screen and go directly to the next screen
                      Get.off(() => widget.nextScreen,
                          transition: Transition.fadeIn);
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.black.withOpacity(0.3),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: Text(
                      'Bỏ qua',
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
