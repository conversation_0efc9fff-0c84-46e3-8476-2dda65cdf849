{"version": 3, "names": ["_index", "require", "_skip", "Symbol", "_stop", "traverseFast", "node", "enter", "opts", "keys", "VISITOR_KEYS", "type", "ret", "undefined", "key", "subNode", "Array", "isArray", "skip", "stop"], "sources": ["../../src/traverse/traverseFast.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\nconst _skip = Symbol();\nconst _stop = Symbol();\n\n/**\n * A prefix AST traversal implementation meant for simple searching and processing.\n * @param enter The callback can return `traverseFast.skip` to skip the subtree of the current node, or `traverseFast.stop` to stop the traversal.\n * @returns `true` if the traversal was stopped by callback, `false` otherwise.\n */\nexport default function traverseFast<Options = object>(\n  node: t.Node | null | undefined,\n  enter: (\n    node: t.Node,\n    opts?: Options,\n  ) => void | typeof traverseFast.skip | typeof traverseFast.stop,\n  opts?: Options,\n): boolean {\n  if (!node) return false;\n\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return false;\n\n  opts = opts || ({} as Options);\n  const ret = enter(node, opts);\n  if (ret !== undefined) {\n    switch (ret) {\n      case _skip:\n        return false;\n      case _stop:\n        return true;\n    }\n  }\n\n  for (const key of keys) {\n    const subNode: t.Node | undefined | null =\n      // @ts-expect-error key must present in node\n      node[key];\n\n    if (!subNode) continue;\n\n    if (Array.isArray(subNode)) {\n      for (const node of subNode) {\n        if (traverseFast(node, enter, opts)) return true;\n      }\n    } else {\n      if (traverseFast(subNode, enter, opts)) return true;\n    }\n  }\n  return false;\n}\n\ntraverseFast.skip = _skip;\ntraverseFast.stop = _stop;\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGA,MAAMC,KAAK,GAAGC,MAAM,CAAC,CAAC;AACtB,MAAMC,KAAK,GAAGD,MAAM,CAAC,CAAC;AAOP,SAASE,YAAYA,CAClCC,IAA+B,EAC/BC,KAG+D,EAC/DC,IAAc,EACL;EACT,IAAI,CAACF,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAMG,IAAI,GAAGC,mBAAY,CAACJ,IAAI,CAACK,IAAI,CAAC;EACpC,IAAI,CAACF,IAAI,EAAE,OAAO,KAAK;EAEvBD,IAAI,GAAGA,IAAI,IAAK,CAAC,CAAa;EAC9B,MAAMI,GAAG,GAAGL,KAAK,CAACD,IAAI,EAAEE,IAAI,CAAC;EAC7B,IAAII,GAAG,KAAKC,SAAS,EAAE;IACrB,QAAQD,GAAG;MACT,KAAKV,KAAK;QACR,OAAO,KAAK;MACd,KAAKE,KAAK;QACR,OAAO,IAAI;IACf;EACF;EAEA,KAAK,MAAMU,GAAG,IAAIL,IAAI,EAAE;IACtB,MAAMM,OAAkC,GAEtCT,IAAI,CAACQ,GAAG,CAAC;IAEX,IAAI,CAACC,OAAO,EAAE;IAEd,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;MAC1B,KAAK,MAAMT,IAAI,IAAIS,OAAO,EAAE;QAC1B,IAAIV,YAAY,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,CAAC,EAAE,OAAO,IAAI;MAClD;IACF,CAAC,MAAM;MACL,IAAIH,YAAY,CAACU,OAAO,EAAER,KAAK,EAAEC,IAAI,CAAC,EAAE,OAAO,IAAI;IACrD;EACF;EACA,OAAO,KAAK;AACd;AAEAH,YAAY,CAACa,IAAI,GAAGhB,KAAK;AACzBG,YAAY,CAACc,IAAI,GAAGf,KAAK", "ignoreList": []}