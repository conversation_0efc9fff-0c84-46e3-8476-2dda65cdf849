import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../controllers/auth_controller.dart';

/// Lớ<PERSON> quản lý chế độ developer
///
/// Lớp này kiểm soát các chức năng debug và chỉ cho phép
/// tài khoản developer sử dụng các chức năng này.
class DeveloperMode extends GetxController {
  static const String _developerModeKey = 'developer_mode_enabled';
  static const List<String> _developerEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  final RxBool _isDeveloperMode = false.obs;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Getter để kiểm tra xem có đang ở chế độ developer không
  bool get isDeveloperMode => _isDeveloperMode.value;

  // Getter để kiểm tra trạng thái developer mode (để debug)
  RxBool get developerModeStatus => _isDeveloperMode;

  @override
  void onInit() {
    super.onInit();
    _loadDeveloperMode();

    // Lắng nghe sự thay đổi trạng thái đăng nhập
    final authController = Get.find<AuthController>();
    ever(authController.isLoggedInObs, (_) {
      _checkDeveloperStatus();
    });
  }

  /// Kiểm tra xem người dùng hiện tại có phải là developer không
  Future<void> _checkDeveloperStatus() async {
    final authController = Get.find<AuthController>();

    if (!authController.isLoggedIn) {
      _isDeveloperMode.value = false;
      print('DeveloperMode: User not logged in');
      return;
    }

    final userEmail = authController.user?.email;
    final userId = authController.user?.id;

    print(
        'DeveloperMode: Checking status for user: $userId, email: $userEmail');

    if (userEmail == null) {
      _isDeveloperMode.value = false;
      print('DeveloperMode: User email is null');
      return;
    }

    // Kiểm tra email trong danh sách developer
    if (_developerEmails.contains(userEmail.toLowerCase())) {
      _isDeveloperMode.value = true;
      _saveDeveloperMode(true);
      print('DeveloperMode: User is developer by email');
      return;
    }

    // Kiểm tra trong Firestore - user_roles collection
    try {
      print('DeveloperMode: Checking Firestore for user_roles/$userId');

      final roleDoc =
          await _firestore.collection('user_roles').doc(userId).get();

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        final role = roleData['role'] as String?;

        print('DeveloperMode: User role from user_roles: $role');

        if (role == 'developer') {
          _isDeveloperMode.value = true;
          _saveDeveloperMode(true);
          print('DeveloperMode: User is developer by user_roles document');
          return;
        }
      } else {
        print('DeveloperMode: Document does not exist in user_roles');
      }
    } catch (e) {
      // In lỗi Firestore
      print('DeveloperMode: Error checking user_roles: $e');
    }

    // Nếu không phải developer, tắt chế độ developer
    _isDeveloperMode.value = false;
    _saveDeveloperMode(false);
    print('DeveloperMode: User is not a developer');
  }

  /// Lưu trạng thái chế độ developer vào SharedPreferences
  Future<void> _saveDeveloperMode(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_developerModeKey, enabled);
    } catch (e) {
      // Bỏ qua lỗi
    }
  }

  /// Tải trạng thái chế độ developer từ SharedPreferences
  Future<void> _loadDeveloperMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final enabled = prefs.getBool(_developerModeKey) ?? false;

      // Nếu đã bật chế độ developer trước đó, kiểm tra lại
      if (enabled) {
        _checkDeveloperStatus();
      } else {
        _isDeveloperMode.value = false;
      }
    } catch (e) {
      _isDeveloperMode.value = false;
    }
  }

  /// Kiểm tra xem có thể hiển thị chức năng debug không
  bool canShowDebugFeature() {
    return _isDeveloperMode.value;
  }

  /// Kiểm tra xem có thể thực hiện hành động debug không
  bool canPerformDebugAction() {
    return _isDeveloperMode.value;
  }

  /// Kiểm tra xem có phải là developer không
  bool isDeveloper() {
    return _isDeveloperMode.value;
  }

  /// Kiểm tra và hiển thị thông tin debug về trạng thái developer
  Future<Map<String, dynamic>> checkAndShowDeveloperStatus() async {
    final authController = Get.find<AuthController>();
    final result = <String, dynamic>{};

    // Thông tin cơ bản
    result['isLoggedIn'] = authController.isLoggedIn;
    result['isDeveloperMode'] = _isDeveloperMode.value;
    result['userId'] = authController.user?.id;
    result['userEmail'] = authController.user?.email;

    if (!authController.isLoggedIn) {
      result['status'] = 'Not logged in';
      return result;
    }

    final userEmail = authController.user?.email;
    final userId = authController.user?.id;

    if (userEmail == null) {
      result['status'] = 'User email is null';
      return result;
    }

    // Kiểm tra email
    if (_developerEmails.contains(userEmail.toLowerCase())) {
      result['status'] = 'Developer by email';
      result['emailInList'] = true;
      return result;
    }

    // Kiểm tra Firestore - user_roles collection
    try {
      final roleDoc =
          await _firestore.collection('user_roles').doc(userId).get();

      result['userRoleExists'] = roleDoc.exists;

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        final role = roleData['role'] as String?;

        result['userRole'] = role;

        if (role == 'developer') {
          result['status'] = 'Developer by user_roles document';
          result['userRoleData'] = roleData;

          // Cập nhật trạng thái
          _isDeveloperMode.value = true;
          _saveDeveloperMode(true);

          return result;
        }
      }
    } catch (e) {
      result['userRoleError'] = e.toString();
    }

    result['status'] = 'Not a developer';

    return result;
  }

  /// Kiểm tra xem có quyền admin không
  /// Developer luôn có quyền admin
  bool hasAdminAccess() {
    if (_isDeveloperMode.value) {
      return true; // Developer luôn có quyền admin
    }

    // Kiểm tra quyền admin từ AuthController
    final authController = Get.find<AuthController>();
    return authController.isAdmin;
  }

  /// Thêm một tài khoản vào danh sách developer
  Future<bool> addDeveloperAccount(String userId) async {
    if (!_isDeveloperMode.value) {
      return false;
    }

    try {
      // Sử dụng collection user_roles thay vì developer_accounts
      await _firestore.collection('user_roles').doc(userId).set({
        'role': 'developer',
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': Get.find<AuthController>().user?.id,
      }, SetOptions(merge: true));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Xóa một tài khoản khỏi danh sách developer (đặt lại thành user)
  Future<bool> removeDeveloperAccount(String userId) async {
    if (!_isDeveloperMode.value) {
      return false;
    }

    try {
      // Đặt lại role thành user thay vì xóa document
      await _firestore.collection('user_roles').doc(userId).set({
        'role': 'user',
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': Get.find<AuthController>().user?.id,
      }, SetOptions(merge: true));
      return true;
    } catch (e) {
      return false;
    }
  }
}
