def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new Exception("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'com.google.gms.google-services'

// Load keystore properties
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    compileSdkVersion 34

    // Thêm cấu hình cho image_picker
    aaptOptions {
        noCompress 'tflite'
        noCompress 'lite'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.example.dop_phim"
        minSdkVersion 21
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            // Cấu hình proguard cho image_picker
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
        }
    }
}

flutter {
    source '../..'
    // You can specify a custom Flutter SDK path if needed
    // flutterSdkPath = "..."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}

// Task để lấy SHA-256 fingerprint cho debug keystore
task getDebugSha256 {
    doLast {
        def keystorePath = "${System.getProperty('user.home')}/.android/debug.keystore"
        def keyAlias = "androiddebugkey"
        def keyPassword = "android"
        def keystorePassword = "android"

        println "Getting SHA-256 fingerprint for debug keystore..."
        exec {
            commandLine 'keytool', '-list', '-v', '-keystore', keystorePath, '-alias', keyAlias, '-storepass', keystorePassword, '-keypass', keyPassword
        }
    }
}

// Task để lấy SHA-256 fingerprint cho release keystore (nếu có)
task getReleaseSha256 {
    doLast {
        def keystorePath = project.hasProperty('storeFile') ? project.storeFile : null
        def keyAlias = project.hasProperty('keyAlias') ? project.keyAlias : null
        def keyPassword = project.hasProperty('keyPassword') ? project.keyPassword : null
        def keystorePassword = project.hasProperty('storePassword') ? project.storePassword : null

        if (keystorePath && keyAlias && keyPassword && keystorePassword) {
            println "Getting SHA-256 fingerprint for release keystore..."
            exec {
                commandLine 'keytool', '-list', '-v', '-keystore', keystorePath, '-alias', keyAlias, '-storepass', keystorePassword, '-keypass', keyPassword
            }
        } else {
            println "Release keystore not configured. Please set up signing config first."
        }
    }
}
