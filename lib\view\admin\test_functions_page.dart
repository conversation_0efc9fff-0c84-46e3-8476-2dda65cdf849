import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/ticket_expiration_service.dart';
import '../../utils/app_colors.dart';

class TestFunctionsPage extends StatefulWidget {
  const TestFunctionsPage({Key? key}) : super(key: key);

  @override
  State<TestFunctionsPage> createState() => _TestFunctionsPageState();
}

class _TestFunctionsPageState extends State<TestFunctionsPage> {
  final TicketExpirationService _expirationService =
      Get.find<TicketExpirationService>();

  bool _isLoading = false;
  String _result = '';

  Future<void> _testManualUpdate() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing manual update...';
    });

    try {
      final result = await _expirationService.updateExpiredTicketsServerSide();
      setState(() {
        _result = 'Manual Update Success:\n${result.toString()}';
      });
    } catch (e) {
      setState(() {
        _result = 'Manual Update Error:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGetStats() async {
    setState(() {
      _isLoading = true;
      _result = 'Getting stats...';
    });

    try {
      final result = await _expirationService.getExpiredTicketsStatsServerSide();
      setState(() {
        _result = 'Stats Success:\n${result.toString()}';
      });
    } catch (e) {
      setState(() {
        _result = 'Stats Error:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testClientSide() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing client-side update...';
    });

    try {
      await _expirationService.forceCheckExpiredTickets();
      setState(() {
        _result = 'Client-side Update Success!';
      });
    } catch (e) {
      setState(() {
        _result = 'Client-side Update Error:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Firebase Functions'),
        backgroundColor: AppColors.primaryBlue,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Firebase Functions Test',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 20),
              
              // Test buttons
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testManualUpdate,
                  icon: const Icon(Icons.cloud_sync),
                  label: const Text('Test Manual Update (Server)'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: AppColors.textPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testGetStats,
                  icon: const Icon(Icons.analytics),
                  label: const Text('Test Get Stats (Server)'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryAmber,
                    foregroundColor: AppColors.textPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testClientSide,
                  icon: const Icon(Icons.phone_android),
                  label: const Text('Test Client-side Update'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.successGreen,
                    foregroundColor: AppColors.textPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Loading indicator
              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(),
                ),
              
              // Result display
              if (_result.isNotEmpty) ...[
                const Text(
                  'Result:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.cardBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.borderPrimary),
                    ),
                    child: SingleChildScrollView(
                      child: Text(
                        _result,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
