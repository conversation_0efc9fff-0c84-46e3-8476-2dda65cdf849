#!/bin/bash

# Firebase Setup Script for Movie Finder App
# This script helps deploy Firebase Realtime Database rules

echo "🔥 Firebase Realtime Database Rules Deployment"
echo "=============================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed"
    echo "📦 Installing Firebase CLI..."
    npm install -g firebase-tools
else
    echo "✅ Firebase CLI is already installed"
fi

# Check if user is logged in
echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase"
    echo "🔑 Please login to Firebase..."
    firebase login
else
    echo "✅ Already logged in to Firebase"
fi

# List available projects
echo "📋 Available Firebase projects:"
firebase projects:list

# Check if firebase.json exists
if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found"
    echo "🔧 Initializing Firebase project..."
    firebase init database
else
    echo "✅ firebase.json found"
fi

# Validate database rules
echo "🔍 Validating database rules..."
if [ -f "database.rules.json" ]; then
    echo "✅ database.rules.json found"
    
    # Check if rules are valid JSON
    if python -m json.tool database.rules.json > /dev/null 2>&1; then
        echo "✅ Rules file is valid JSON"
    else
        echo "❌ Rules file has invalid JSON syntax"
        exit 1
    fi
else
    echo "❌ database.rules.json not found"
    exit 1
fi

# Deploy rules
echo "🚀 Deploying database rules..."
firebase deploy --only database

if [ $? -eq 0 ]; then
    echo "✅ Database rules deployed successfully!"
    echo ""
    echo "📊 Next steps:"
    echo "1. Check Firebase Console > Realtime Database > Rules"
    echo "2. Verify indexes are applied"
    echo "3. Test the app to ensure no more index errors"
    echo ""
    echo "🔗 Firebase Console: https://console.firebase.google.com/"
else
    echo "❌ Failed to deploy database rules"
    echo "💡 Try deploying manually from Firebase Console"
    exit 1
fi

echo "🎉 Setup complete!"
