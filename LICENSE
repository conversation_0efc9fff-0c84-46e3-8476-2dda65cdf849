MIT License

Copyright (c) 2024 Đớp Phim - Movie Ticket Booking App

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Third-Party Licenses

This project uses several third-party packages and services. Please refer to their respective licenses:

### Flutter Framework
- License: BSD 3-Clause License
- Copyright: Google Inc.

### Firebase Services
- License: Apache License 2.0
- Copyright: Google Inc.

### GetX State Management
- License: MIT License
- Copyright: Jonny Borges

### PayPal SDK
- License: PayPal Developer License
- Copyright: PayPal Inc.

### Media Kit
- License: MIT License
- Copyright: Hitesh Kumar Saini

### Other Dependencies
Please refer to the pubspec.yaml file for a complete list of dependencies and their respective licenses.

## Attribution

This project includes assets and resources that may require attribution:

### Icons and Images
- Material Design Icons: Apache License 2.0
- Custom app icons: Created specifically for this project

### Fonts
- Google Fonts: SIL Open Font License

### Sample Data
- Movie data: For demonstration purposes only
- Images: Used under fair use for educational purposes

## Disclaimer

This application is designed for educational and demonstration purposes. The developers are not responsible for:

1. Any misuse of the application
2. Data loss or security breaches
3. Payment processing issues
4. Compliance with local regulations
5. Third-party service availability

Users are responsible for:
- Ensuring compliance with local laws and regulations
- Securing their Firebase and PayPal credentials
- Implementing appropriate security measures for production use
- Obtaining necessary licenses for commercial use

## Contact

For licensing questions or concerns, please contact the project maintainers through GitHub issues.
