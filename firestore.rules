rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    // Hàm hỗ trợ kiểm tra người dùng đã xác thực
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user is developer
    // Hàm hỗ trợ kiểm tra người dùng có vai trò developer
    function isDeveloper() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/user_roles/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/user_roles/$(request.auth.uid)).data.role == 'developer';
    }

    // Helper function to check if user is admin
    // Hàm hỗ trợ kiểm tra người dùng có vai trò admin
    function isAdmin() {
      return isAuthenticated() && (
        // Check if user is developer (developers have admin rights)
        // Kiểm tra nếu người dùng là developer (developer c<PERSON> quyền admin)
        isDeveloper() ||
        // Check if user has admin role
        // Kiểm tra nếu người dùng có vai trò admin
        (exists(/databases/$(database)/documents/user_roles/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/user_roles/$(request.auth.uid)).data.role == 'admin')
      );
    }

    // Helper function to check if user is accessing their own data
    // Hàm hỗ trợ kiểm tra người dùng đang truy cập dữ liệu của chính họ
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Helper function to check if user is active
    // Hàm hỗ trợ kiểm tra người dùng đang hoạt động
    function isActiveUser() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;
    }

    // Users collection - Bộ sưu tập người dùng
    match /users/{userId} {
      // Anyone can read user profiles - Bất kỳ ai cũng có thể đọc hồ sơ người dùng
      // Only the user or an admin can write to their profile - Chỉ người dùng hoặc admin mới có thể ghi vào hồ sơ
      allow read: if isAuthenticated();
      allow create: if isOwner(userId);
      allow update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }

    // User roles collection - Bộ sưu tập vai trò người dùng
    match /user_roles/{userId} {
      // Only admins can read and write user roles - Chỉ admin mới có thể đọc và ghi vai trò người dùng
      // Exception: users can read their own role - Ngoại lệ: người dùng có thể đọc vai trò của chính họ
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isAdmin();
    }

    // Banners collection - Bộ sưu tập banner
    match /banners/{bannerId} {
      // Anyone can read banners - Bất kỳ ai cũng có thể đọc banner
      // Only admins can write to banners - Chỉ admin mới có thể ghi banner
      allow read: if true;
      allow write: if isAdmin();
    }

    // Notifications collection - Bộ sưu tập thông báo
    match /notifications/{notificationId} {
      // Anyone can read notifications - Bất kỳ ai cũng có thể đọc thông báo
      // Only admins can write to notifications - Chỉ admin mới có thể ghi thông báo
      allow read: if true;
      allow write: if isAdmin();
    }

    // User notifications collection - Bộ sưu tập thông báo người dùng
    match /user_notifications/{userNotificationId} {
      // Users can only read their own notification status
      // Người dùng chỉ có thể đọc trạng thái thông báo của chính họ
      allow read: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;

      // Users can only create and update their own notification status
      // Người dùng chỉ có thể tạo và cập nhật trạng thái thông báo của chính họ
      allow create, update: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid;

      // Only admins and developers can delete notification status
      // Chỉ admin và developer có thể xóa trạng thái thông báo
      allow delete: if isAdmin() || isDeveloper();
    }

    // Movies collection - Bộ sưu tập phim
    match /movies/{movieId} {
      // Anyone can read movies - Bất kỳ ai cũng có thể đọc phim
      // Only admins can write to movies - Chỉ admin mới có thể ghi phim
      allow read: if true;
      allow write: if isAdmin();
    }

    // Favorites collection - Bộ sưu tập yêu thích
    match /favorites/{favoriteId} {
      // Users can read and write their own favorites - Người dùng có thể đọc và ghi danh sách yêu thích của họ
      // Admins can read all favorites - Admin có thể đọc tất cả danh sách yêu thích
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow update, delete: if isOwner(resource.data.userId) || isAdmin();
    }

    // Tickets collection - Bộ sưu tập vé
    match /tickets/{ticketId} {
      // Users can read and write their own tickets - Người dùng có thể đọc và ghi vé của họ
      // Admins can read all tickets - Admin có thể đọc tất cả vé
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isAdmin();
    }

    // Theaters collection - Bộ sưu tập rạp chiếu
    match /theaters/{theaterId} {
      // Public read - Đọc công khai
      allow read: if true;
      allow write: if isAdmin();
    }

    // Screens collection - Bộ sưu tập màn hình chiếu
    match /screens/{screenId} {
      // Public read - Đọc công khai
      allow read: if true;
      allow write: if isAdmin();
    }

    // Showtimes collection - Bộ sưu tập lịch chiếu
    match /showtimes/{showtimeId} {
      // Public read - Đọc công khai
      allow read: if true;
      allow create: if isAdmin();
      // Authenticated users can book seats - Người dùng đã xác thực có thể đặt ghế
      allow update: if isAuthenticated() || isAdmin();
      allow delete: if isAdmin();
    }

    // Payments collection - Bộ sưu tập thanh toán
    match /payments/{paymentId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      // Users can update their own payments - Người dùng có thể cập nhật thanh toán của họ
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isAdmin();
    }

    // Reviews collection - Bộ sưu tập đánh giá
    match /reviews/{reviewId} {
      allow read: if resource.data.status == 'active' || isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }

    // Promotions collection - Bộ sưu tập khuyến mãi
    match /promotions/{promotionId} {
      allow read: if resource.data.isActive == true || isAdmin();
      allow write: if isAdmin();
    }

    // User promotions collection - Bộ sưu tập khuyến mãi người dùng
    match /user_promotions/{userPromotionId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update, delete: if isAdmin();
    }

    // Loyalty transactions collection - Bộ sưu tập giao dịch tích điểm
    match /loyalty_transactions/{transactionId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update, delete: if isAdmin();
    }

    // Bug reports collection - Bộ sưu tập báo cáo lỗi
    match /bug_reports/{bugReportId} {
      // Users can read their own bug reports - Người dùng có thể đọc báo cáo lỗi của họ
      // Admins and developers can read all bug reports - Admin và developer có thể đọc tất cả báo cáo lỗi
      allow read: if isAuthenticated() &&
        (resource.data.reportedBy == request.auth.uid || isAdmin() || isDeveloper());

      // Users can create bug reports - Người dùng có thể tạo báo cáo lỗi
      allow create: if isAuthenticated() &&
        request.resource.data.reportedBy == request.auth.uid;

      // Only admins and developers can update bug reports - Chỉ admin và developer có thể cập nhật báo cáo lỗi
      allow update: if isAdmin() || isDeveloper();

      // Only admins can delete bug reports - Chỉ admin có thể xóa báo cáo lỗi
      allow delete: if isAdmin();
    }

    // Default deny all - Từ chối tất cả theo mặc định
    match /{document=**} {
      allow read, write: if false;
    }
  }
}