// Original file: proto/channelz.proto

import type { Subchannel as _grpc_channelz_v1_Subchannel, Subchannel__Output as _grpc_channelz_v1_Subchannel__Output } from '../../../grpc/channelz/v1/Subchannel';

export interface GetSubchannelResponse {
  /**
   * The Subchannel that corresponds to the requested subchannel_id.  This
   * field should be set.
   */
  'subchannel'?: (_grpc_channelz_v1_Subchannel | null);
}

export interface GetSubchannelResponse__Output {
  /**
   * The Subchannel that corresponds to the requested subchannel_id.  This
   * field should be set.
   */
  'subchannel': (_grpc_channelz_v1_Subchannel__Output | null);
}
