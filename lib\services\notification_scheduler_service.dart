import 'dart:async';
import 'package:get/get.dart';
import 'realtime_database_service.dart';
import '../controllers/realtime_notification_controller.dart';

class NotificationSchedulerService extends GetxService {
  // ignore: unused_field
  final RealtimeDatabaseService _realtimeService =
      Get.find<RealtimeDatabaseService>();

  Timer? _scheduledNotificationTimer;
  Timer? _cleanupTimer;
  Timer? _dailyResetTimer;

  // Observables for monitoring
  final RxInt _processedScheduledNotifications = 0.obs;
  final RxInt _cleanedUpNotifications = 0.obs;
  final RxBool _isRunning = false.obs;

  // Getters
  int get processedScheduledNotifications =>
      _processedScheduledNotifications.value;
  int get cleanedUpNotifications => _cleanedUpNotifications.value;
  bool get isRunning => _isRunning.value;

  @override
  void onInit() {
    super.onInit();
    startSchedulers();
  }

  @override
  void onClose() {
    stopSchedulers();
    super.onClose();
  }

  // Start all schedulers
  void startSchedulers() {
    _isRunning.value = true;

    // Start scheduled notification processing (every minute)
    _scheduledNotificationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _processScheduledNotifications(),
    );

    // Start cleanup timer (every 6 hours)
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 6),
      (_) => _cleanupExpiredNotifications(),
    );

    // Start daily reset timer (every day at midnight)
    _scheduleDailyReset();

    print('NotificationSchedulerService: All schedulers started');
  }

  // Stop all schedulers
  void stopSchedulers() {
    _isRunning.value = false;

    _scheduledNotificationTimer?.cancel();
    _cleanupTimer?.cancel();
    _dailyResetTimer?.cancel();

    _scheduledNotificationTimer = null;
    _cleanupTimer = null;
    _dailyResetTimer = null;

    print('NotificationSchedulerService: All schedulers stopped');
  }

  // Process scheduled notifications
  Future<void> _processScheduledNotifications() async {
    try {
      // Temporary disable until Firebase rules are deployed
      print(
          'NotificationSchedulerService: Scheduled notifications processing temporarily disabled');
      print(
          'NotificationSchedulerService: Please deploy Firebase database rules with status index');
      print(
          'NotificationSchedulerService: See QUICK_FIX_INDEX_ERROR.md for instructions');

      // Uncomment after deploying rules:
      // await _realtimeService.processScheduledNotifications();
      // _processedScheduledNotifications.value++;
      // print('NotificationSchedulerService: Processed scheduled notifications');
    } catch (e) {
      print(
          'NotificationSchedulerService: Error processing scheduled notifications: $e');
    }
  }

  // Cleanup expired notifications
  Future<void> _cleanupExpiredNotifications() async {
    try {
      // Temporary disable until Firebase rules are deployed
      print('NotificationSchedulerService: Cleanup temporarily disabled');
      print(
          'NotificationSchedulerService: Please deploy Firebase database rules first');

      // Uncomment after deploying rules:
      // await _realtimeService.cleanupExpiredNotifications();
      // _cleanedUpNotifications.value++;
      // print('NotificationSchedulerService: Cleaned up expired notifications');
    } catch (e) {
      print(
          'NotificationSchedulerService: Error cleaning up notifications: $e');
    }
  }

  // Schedule daily reset
  void _scheduleDailyReset() {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final timeUntilMidnight = tomorrow.difference(now);

    _dailyResetTimer = Timer(timeUntilMidnight, () {
      _performDailyReset();

      // Schedule the next daily reset
      _dailyResetTimer = Timer.periodic(
        const Duration(days: 1),
        (_) => _performDailyReset(),
      );
    });
  }

  // Perform daily reset
  void _performDailyReset() {
    try {
      // Reset daily notification counts for all controllers
      if (Get.isRegistered<RealtimeNotificationController>()) {
        final notificationController =
            Get.find<RealtimeNotificationController>();
        notificationController.resetDailyNotificationCount();
      }

      print('NotificationSchedulerService: Daily reset completed');
    } catch (e) {
      print('NotificationSchedulerService: Error during daily reset: $e');
    }
  }

  // Manual triggers for testing/admin purposes
  Future<void> manualProcessScheduledNotifications() async {
    await _processScheduledNotifications();
  }

  Future<void> manualCleanupExpiredNotifications() async {
    await _cleanupExpiredNotifications();
  }

  void manualDailyReset() {
    _performDailyReset();
  }

  // Get scheduler status
  Map<String, dynamic> getSchedulerStatus() {
    return {
      'isRunning': _isRunning.value,
      'processedScheduledNotifications': _processedScheduledNotifications.value,
      'cleanedUpNotifications': _cleanedUpNotifications.value,
      'scheduledNotificationTimerActive':
          _scheduledNotificationTimer?.isActive ?? false,
      'cleanupTimerActive': _cleanupTimer?.isActive ?? false,
      'dailyResetTimerActive': _dailyResetTimer?.isActive ?? false,
    };
  }

  // Restart schedulers
  void restartSchedulers() {
    stopSchedulers();
    Future.delayed(const Duration(seconds: 1), () {
      startSchedulers();
    });
  }
}
