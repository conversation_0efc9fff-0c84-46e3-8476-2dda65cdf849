/// Firebase configuration service for app initialization and security
/// Dịch vụ cấu hình Firebase cho khởi tạo ứng dụng và bảo mật
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui' as ui;
import 'debug_firebase_config.dart';

class FirebaseConfig {
  /// Initialize Firebase App Check for security (production only)
  /// Khởi tạo Firebase App Check cho bảo mật (chỉ production)
  static Future<void> initializeAppCheck() async {
    try {
      // Skip App Check in debug mode to avoid attestation errors
      // Bỏ qua App Check ở chế độ debug để tránh lỗi xác thực
      if (kDebugMode) {
        print(
            'Skipping Firebase App Check in debug mode to avoid attestation errors');
        return;
      }

      // Initialize App Check for production security
      // Khởi tạo App Check cho bảo mật production
      await FirebaseAppCheck.instance.activate(
        webProvider: ReCaptchaV3Provider('your-recaptcha-site-key'),
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.deviceCheck,
      );

      if (kDebugMode) {
        print('Firebase App Check initialized successfully');
      }
    } catch (e) {
      // App Check is optional, don't crash app if it fails
      // App Check là tùy chọn, không crash app nếu thất bại
      if (kDebugMode) {
        print('Firebase App Check initialization failed: $e');
      }
    }
  }

  /// Configure Firebase locale to reduce warnings
  /// Cấu hình locale Firebase để giảm cảnh báo
  static void configureFirebaseLocale() {
    if (kDebugMode) {
      // Use debug configuration in debug mode
      // Sử dụng cấu hình debug ở chế độ debug
      DebugFirebaseConfig.configureForDebug();
      DebugFirebaseConfig.disableProblematicFeatures();
      return;
    }

    try {
      // Set Firebase Auth language to device locale
      // Đặt ngôn ngữ Firebase Auth theo locale thiết bị
      final locale = ui.PlatformDispatcher.instance.locale;
      FirebaseAuth.instance.setLanguageCode(locale.languageCode);

      if (kDebugMode) {
        print('Firebase locale set to: ${locale.languageCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Firebase locale configuration failed: $e');
      }
    }
  }

  /// Configure Firebase Auth settings and listeners
  /// Cấu hình thiết lập và listeners Firebase Auth
  static Future<void> configureFirebaseAuth() async {
    try {
      // Listen to auth state changes for debugging
      // Lắng nghe thay đổi trạng thái auth để debug
      FirebaseAuth.instance.authStateChanges().listen((User? user) {
        if (kDebugMode) {
          print('Firebase Auth state changed: ${user?.email ?? 'No user'}');
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('Firebase Auth configuration failed: $e');
      }
    }
  }
}
