/// Ticket controller for managing user tickets with Firestore and local storage
/// Controller vé để quản lý vé người dùng với Firestore và lưu trữ local
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/ticket_model.dart';
import '../controllers/auth_controller.dart';
import '../services/ticket_service.dart';
import '../services/ticket_expiration_service.dart';

class TicketController extends GetxController {
  // Service instance for ticket operations
  // Instance service cho các thao tác vé
  final TicketService _ticketService = TicketService();

  // Observable ticket lists
  // Danh sách vé observable
  final RxList<Ticket> tickets = <Ticket>[].obs; // All tickets / Tất cả vé
  final RxList<Ticket> upcomingTickets =
      <Ticket>[].obs; // Future tickets / Vé tương lai
  final RxList<Ticket> pastTickets =
      <Ticket>[].obs; // Past/expired tickets / Vé đã qua/hết hạn
  final RxBool isLoading = false.obs; // Loading state / Trạng thái loading
  final RxString errorMessage = ''.obs; // Error messages / Thông báo lỗi

  late AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _authController = Get.find<AuthController>();
    loadTickets(); // Load tickets on initialization / Tải vé khi khởi tạo

    // Listen to authentication changes to reload tickets
    // Lắng nghe thay đổi xác thực để tải lại vé
    ever(_authController.isLoggedInObs, (_) {
      loadTickets();
    });
  }

  /// Load tickets from appropriate source (Firestore or local storage)
  /// Tải vé từ nguồn phù hợp (Firestore hoặc lưu trữ local)
  Future<void> loadTickets() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      tickets.clear();
      upcomingTickets.clear();
      pastTickets.clear();

      if (_authController.isLoggedIn) {
        // User is authenticated, load from Firestore
        // Người dùng đã xác thực, tải từ Firestore
        await _loadFromFirestore();
      } else {
        // User not authenticated, load from local storage
        // Người dùng chưa xác thực, tải từ lưu trữ local
        await _loadFromLocalStorage();
      }

      // Categorize tickets by date and status
      // Phân loại vé theo ngày và trạng thái
      _categorizeTickets();
    } catch (e) {
      errorMessage.value = 'Failed to load tickets: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// Load tickets from Firestore for authenticated users
  /// Tải vé từ Firestore cho người dùng đã xác thực
  Future<void> _loadFromFirestore() async {
    try {
      print('TicketController: Loading tickets from Firestore...');
      final loadedTickets = await _ticketService.getUserTickets();
      print(
          'TicketController: Loaded ${loadedTickets.length} tickets from Firestore');

      // Debug logging for ticket details
      // Ghi log debug cho chi tiết vé
      for (var ticket in loadedTickets) {
        print(
            'TicketController: Ticket - ${ticket.movieTitle} (${ticket.id}) - Date: ${ticket.date} - Status: ${ticket.status.name}');
      }

      tickets.value = loadedTickets;
    } catch (e) {
      print('TicketController: Error loading from Firestore: $e');
      // Fallback to local storage if Firestore fails
      // Dự phòng lưu trữ local nếu Firestore thất bại
      await _loadFromLocalStorage();
    }
  }

  /// Categorize tickets into upcoming and past based on date and status
  /// Phân loại vé thành sắp tới và đã qua dựa trên ngày và trạng thái
  void _categorizeTickets() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    print('TicketController: Categorizing ${tickets.length} tickets...');
    print('TicketController: Today is: $today');

    final upcoming = <Ticket>[];
    final past = <Ticket>[];

    for (final ticket in tickets) {
      try {
        final ticketDate = DateTime.parse(ticket.date);
        print(
            'TicketController: Ticket ${ticket.movieTitle} - Date: ${ticket.date} -> Parsed: $ticketDate - Status: ${ticket.status.name}');

        // Only categorize active tickets (not cancelled)
        // Chỉ phân loại vé đang hoạt động (không bị hủy)
        if (ticket.status != TicketStatus.cancelled) {
          if (ticketDate.isAfter(today) || ticketDate.isAtSameMomentAs(today)) {
            upcoming.add(ticket);
            print('TicketController: -> Added to upcoming');
          } else {
            past.add(ticket);
            print('TicketController: -> Added to past');
          }
        } else {
          print('TicketController: -> Skipped (cancelled)');
        }
      } catch (e) {
        print('TicketController: Error parsing date ${ticket.date}: $e');
        // If date parsing fails, consider as past ticket (only if not cancelled)
        // Nếu phân tích ngày thất bại, coi như vé đã qua (chỉ nếu không bị hủy)
        if (ticket.status != TicketStatus.cancelled) {
          past.add(ticket);
        }
      }
    }

    print(
        'TicketController: Categorization complete - Upcoming: ${upcoming.length}, Past: ${past.length}');

    upcomingTickets.value = upcoming;
    pastTickets.value = past;
  }

  /// Load tickets from local storage for offline access
  /// Tải vé từ lưu trữ local để truy cập offline
  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ticketsJson = prefs.getStringList('tickets') ?? [];

      final List<Ticket> loadedTickets = [];
      for (var json in ticketsJson) {
        final Map<String, dynamic> data = jsonDecode(json);

        // Convert string dates back to Timestamp for consistency
        // Chuyển đổi ngày chuỗi trở lại Timestamp để đảm bảo tính nhất quán
        final timestampFields = [
          'purchase_date',
          'purchaseDate',
          'cancelled_at',
          'cancelledAt',
          'used_at',
          'usedAt'
        ];

        for (String field in timestampFields) {
          if (data[field] is String && data[field] != null) {
            try {
              data[field] = Timestamp.fromDate(DateTime.parse(data[field]));
            } catch (e) {
              print('TicketController: Error parsing date field $field: $e');
            }
          }
        }

        loadedTickets.add(Ticket.fromJson(data));
      }

      // Sort by purchase date (newest first)
      // Sắp xếp theo ngày mua (mới nhất trước)
      loadedTickets.sort((a, b) => b.purchaseDate.compareTo(a.purchaseDate));
      tickets.value = loadedTickets;
    } catch (e) {
      print('TicketController: Error loading from local storage: $e');
      errorMessage.value = 'Failed to load tickets from local storage: $e';
    }
  }

  /// Purchase a new ticket and save to appropriate storage
  /// Mua vé mới và lưu vào kho lưu trữ phù hợp
  Future<bool> purchaseTicket(Ticket ticket) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      if (_authController.isLoggedIn) {
        // Create ticket in Firestore for authenticated users
        // Tạo vé trong Firestore cho người dùng đã xác thực
        final createdTicket = await _ticketService.createTicket(ticket);
        tickets.add(createdTicket);
      } else {
        // Add to local list for guest users
        // Thêm vào danh sách local cho người dùng khách
        tickets.add(ticket);
      }

      // Always save to local storage as backup
      // Luôn lưu vào lưu trữ local làm bản sao lưu
      await _saveToLocalStorage();

      // Recategorize tickets after adding new one
      // Phân loại lại vé sau khi thêm vé mới
      _categorizeTickets();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to purchase ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // ===== TICKET ACCESS METHODS =====
  // ===== CÁC PHƯƠNG THỨC TRUY CẬP VÉ =====

  /// Get list of upcoming tickets
  /// Lấy danh sách vé sắp tới
  List<Ticket> getUpcomingTickets() {
    return upcomingTickets.toList();
  }

  /// Get list of past/expired tickets
  /// Lấy danh sách vé đã qua/hết hạn
  List<Ticket> getPastTickets() {
    return pastTickets.toList();
  }

  /// Manually check and update expired tickets
  /// Kiểm tra và cập nhật vé hết hạn thủ công
  Future<void> checkExpiredTickets() async {
    try {
      if (_authController.isLoggedIn) {
        // Use expiration service to check expired tickets
        // Sử dụng dịch vụ hết hạn để kiểm tra vé hết hạn
        final expirationService = Get.find<TicketExpirationService>();
        await expirationService.forceCheckExpiredTickets();

        // Reload tickets to reflect status changes
        // Tải lại vé để phản ánh thay đổi trạng thái
        await loadTickets();
      }
    } catch (e) {
      print('TicketController: Error checking expired tickets: $e');
      errorMessage.value = 'Failed to check expired tickets: $e';
    }
  }

  /// Cancel a ticket and update its status
  /// Hủy vé và cập nhật trạng thái
  Future<bool> cancelTicket(String ticketId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      if (_authController.isLoggedIn) {
        // Cancel ticket in Firestore
        // Hủy vé trong Firestore
        await _ticketService.cancelTicket(ticketId,
            0.0); // Refund amount can be calculated / Số tiền hoàn lại có thể được tính toán
      }

      // Update local ticket status instead of removing
      // Cập nhật trạng thái vé local thay vì xóa
      final ticketIndex = tickets.indexWhere((ticket) => ticket.id == ticketId);
      if (ticketIndex != -1) {
        tickets[ticketIndex] = tickets[ticketIndex].copyWith(
          status: TicketStatus.cancelled,
          cancelledAt: DateTime.now(),
        );
      }

      // Save changes to local storage
      // Lưu thay đổi vào lưu trữ local
      await _saveToLocalStorage();

      // Recategorize tickets after cancellation
      // Phân loại lại vé sau khi hủy
      _categorizeTickets();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to cancel ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Save tickets to local storage as backup
  /// Lưu vé vào lưu trữ local làm bản sao lưu
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final List<String> ticketsJson = tickets.map((ticket) {
        final Map<String, dynamic> data = ticket.toJson();

        // Convert Timestamp fields to string for local storage compatibility
        // Chuyển đổi trường Timestamp thành chuỗi để tương thích lưu trữ local
        final timestampFields = [
          'purchase_date',
          'purchaseDate',
          'cancelled_at',
          'cancelledAt',
          'used_at',
          'usedAt'
        ];

        for (String field in timestampFields) {
          if (data[field] is Timestamp) {
            data[field] = (data[field] as Timestamp).toDate().toIso8601String();
          }
        }

        return jsonEncode(data);
      }).toList();

      await prefs.setStringList('tickets', ticketsJson);
    } catch (e) {
      print('TicketController: Error saving to local storage: $e');
      errorMessage.value = 'Failed to save tickets to local storage: $e';
    }
  }
}
